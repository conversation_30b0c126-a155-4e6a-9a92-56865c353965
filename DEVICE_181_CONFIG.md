# ZK Biometric Device 181 Configuration

## Device Details
- **Device ID:** 181
- **Common Key:** 1302
- **Server IP:** *************
- **Server Port:** 32150
- **Connection Type:** PC/Ethernet

## Quick Start

### 1. Start the System
```bash
# Option 1: Use startup script
start_device_181.bat

# Option 2: Manual start
python app.py
```

### 2. Access Web Interface
- **Main Application:** http://localhost:5000
- **Cloud Dashboard:** http://localhost:5000/cloud_dashboard

### 3. Test Device Connection
```bash
# Test device 181 specifically
python test_device_181.py

# Or use the configuration script
python configure_device_181.py
```

## Device Configuration

### Connection Settings
```python
device = ZKBiometricDevice(
    device_ip='*************',
    port=32150,
    device_id='181',
    timeout=15
)
```

### Common Key Usage
The common key (1302) is used for:
- Device authentication
- Secure communication
- Data encryption
- Access control

## Troubleshooting

### Connection Issues
1. **Check Network Connectivity**
   ```bash
   ping *************
   ```

2. **Verify Port Access**
   ```bash
   telnet ************* 32150
   ```

3. **Test Device Response**
   ```bash
   python test_device_181.py
   ```

### Common Problems
- **Timeout Errors:** Increase timeout value or check network
- **Authentication Errors:** Verify common key 1302 is correct
- **Permission Errors:** Check device access permissions
- **Network Errors:** Verify IP ************* is reachable

## Configuration Files
- `.env` - Environment variables
- `cloud_config.json` - Device settings
- `configure_device_181.py` - Device-specific configuration

## Support
- Check system logs for detailed error messages
- Use debug mode for troubleshooting
- Test with minimal configuration first
